// @import "~antd/dist/antd.css";
@import './normalize.less';
@import 'antd/dist/antd.css';
@import './public/colors.less';
@import './public/layout.less';

body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans',
        'Droid Sans', 'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}
.ant-btn.ant-btn-primary {
    background: @blueBtn;
}
.toolTips {
    // background: #F7F8FA;
    box-shadow: '10px 5px 10px rgba(0,0,0,.25) -5px -5px 5px rgba(0,0,0,.25) 10px 10px #f00' !important;
    color: #ddd;
}
// 滚动条样式
::-webkit-scrollbar {
    background-color: transparent;
    width: 12px;
    height: 12px;
}

::-webkit-scrollbar-thumb {
    background-color: inherit;
    border-radius: 8px;
    background-clip: content-box;
    border: 2px solid transparent;
    background-color: slategrey;
}

.ant-tooltip-inner {
    box-shadow: none;
}

.tooltip {
    position: relative;
    .ant-tooltip-inner {
        width: 520px !important;
    }
}
.partiesManageSearchContainer {
    .ant-row {
        flex: 1;
    }
}
// 修改整体input样式
.ant-input-affix-wrapper {
    border-color: #80a932;
}
.ant-input-affix-wrapper:hover {
    border-color: #80a932 !important;
}
// 下拉框
.ant-select-selector {
    border-color: #80a932 !important;
}
// 全局时间选择框
.ant-picker {
    border: 1px solid #80a932;
}

// 全部的input框
.ant-input {
    border: 1px solid #80a932;
}
// 头部
.ant-layout-header {
    background: #f2f8f2 !important;
}
.ant-layout-content {
    background-color: rgba(255, 255, 255, 0.5); /* 红色背景，50%不透明度 */
    color: #000; /* 文本颜色为黑色，100%不透明度 */
}
// 分页
.ant-pagination-item-active {
    background: #80a932 !important;
    border-color: #80a932 !important;
}
.ant-pagination-item-active:hover {
    border-color: #80a932 !important;
}
.checkRecords {
    .ant-pagination-item:hover {
        border-color: #80a932 !important;
    }

    .ant-pagination-item a {
        color: #666666;
    }
    .ant-pagination-item:hover a {
        color: #666666;
    }
}

.ant-pagination-item:hover a {
    color: #80a932;
}

// 按钮颜色
.ant-btn-primary:not(:disabled) {
    color: white;
    background-color: #80a932 !important;
    border: #80a932 !important;
}
.ant-btn:active {
    color: #80a932;
    // color: #fff;
    border-color: #80a932;
}
// .ant-btn:hover, .ant-btn:focus{
//   color: #80a932;
//   border-color: #80a932;
// }
// .ant-btn-default:not(:disabled) {
//   color: #80a932;
//   border-color: #80a932;
// }
.primaryBtn:focus {
    color: #80a932 !important;
    border-color: #80a932 !important;
}
.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    background-color: #7fa93223 !important;
}
.ant-select-item-option-selected:not(.ant-select-item-option-disabled) .ant-select-item-option-state {
    color: #80a932;
}

.ant-upload {
    .ant-btn:hover,
    .ant-btn:focus {
        color: #80a932;
        border-color: #80a932;
    }
}
// 新建生产批次按钮点击样式
.containerBtn {
    .ant-upload {
        .ant-btn:hover,
        .ant-btn:focus {
            color: #80a932;
            border-color: #80a932;
        }
    }
    .ant-btn:active {
        color: #fff;
    }
    .ant-btn:hover,
    .ant-btn:focus {
        color: #fff;
        border-color: #80a932;
    }
}
.ant-popover-inner {
    .ant-btn-primary:hover,
    .ant-btn-primary:focus {
        color: #fff;
        border-color: #80a932;
    }
}
.ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled),
.ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover {
    background-color: #7fa93223 !important;
}

.ant-radio-checked .ant-radio-inner {
    border-color: #76ae55 !important;
}
.ant-radio-inner::after {
    background-color: #76ae55 !important;
}
.ant-radio-wrapper:hover .ant-radio,
.ant-radio:hover .ant-radio-inner,
.ant-radio-input:focus + .ant-radio-inner {
    border-color: #76ae55 !important;
}
.ant-radio-input:focus + .ant-radio-inner {
    box-shadow: 0 0 0 3px rgba(34, 110, 27, 0.2);
}
.ant-table-tbody > tr.ant-table-row-selected > td {
    background: #f2f8f2;
}
.ant-table-tbody > tr.ant-table-row-selected:hover > td {
    background: #f2f8f2;
}
.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused {
    border-color: #76ae55 !important;
    box-shadow: 0 0 0 3px rgba(34, 110, 27, 0.2);
}
.ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner,
.ant-picker-cell-in-view.ant-picker-cell-range-start .ant-picker-cell-inner,
.ant-picker-cell-in-view.ant-picker-cell-range-end .ant-picker-cell-inner {
    background: #76ae55 !important;
}
.ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before {
    border-color: #76ae55 !important;
}
.ant-picker-cell-in-view.ant-picker-cell-in-range::before {
    background: #edf3e3 !important;
}
.ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single)::before,
.ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single)::before {
    background: #edf3e3 !important;
}
.ant-picker-date-panel
    .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start
    .ant-picker-cell-inner::after,
.ant-picker-date-panel
    .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end
    .ant-picker-cell-inner::after {
    background: #edf3e3 !important;
}
.ant-picker-cell-in-view.ant-picker-cell-range-hover-start:not(.ant-picker-cell-in-range):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end)::after,
.ant-picker-cell-in-view.ant-picker-cell-range-hover-end:not(.ant-picker-cell-in-range):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end)::after,
.ant-picker-cell-in-view.ant-picker-cell-range-hover-start.ant-picker-cell-range-start-single::after,
.ant-picker-cell-in-view.ant-picker-cell-range-hover-start.ant-picker-cell-range-start.ant-picker-cell-range-end.ant-picker-cell-range-end-near-hover::after,
.ant-picker-cell-in-view.ant-picker-cell-range-hover-end.ant-picker-cell-range-start.ant-picker-cell-range-end.ant-picker-cell-range-start-near-hover::after,
.ant-picker-cell-in-view.ant-picker-cell-range-hover-end.ant-picker-cell-range-end-single::after,
.ant-picker-cell-in-view.ant-picker-cell-range-hover:not(.ant-picker-cell-in-range)::after {
    border-top: 1px dashed #76ae55 !important;
    border-bottom: 1px dashed #76ae55 !important;
}
tr > .ant-picker-cell-in-view.ant-picker-cell-range-hover:last-child::after,
tr > .ant-picker-cell-in-view.ant-picker-cell-range-hover-start:last-child::after,
.ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover-edge-end.ant-picker-cell-range-hover-edge-end-near-range::after,
.ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-end:not(.ant-picker-cell-range-hover-edge-end-near-range)::after,
.ant-picker-cell-in-view.ant-picker-cell-range-hover-end::after {
    border-right: 1px dashed #76ae55 !important;
}
.ant-input-number-focused {
    border-color: #76ae55 !important;
    box-shadow: 0 0 0 3px rgba(34, 110, 27, 0.2);
}
.ant-input-number:hover {
    border-color: #76ae55 !important;
    box-shadow: 0 0 0 3px rgba(34, 110, 27, 0.2);
}
.ant-input-group-addon .ant-select-open .ant-select-selector,
.ant-input-group-addon .ant-select-focused .ant-select-selector {
    color: #000;
}
