import { useRef, useState } from 'react';
import { Col, Form, message, Row, Space, Badge, InputNumber, Card, Switch, Tooltip } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQuery } from 'react-query';
import dayjs from 'dayjs';

import WithPaginate from '../../hoc/withpaginate';
import BaseCard from '@components/base-card';
import BaseModal from '@components/base-modal';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import BaseButton from '@components/base-button';
import BaseInput from '@components/base-input/base-input';
import BaseSelect from '@components/base-select/base-select';
import BaseDatePicker from '@components/base-date-picker';

import { SearchOutlined, PlusOutlined, DownOutlined, UpOutlined, SyncOutlined } from '@ant-design/icons';

import styles from './index.module.less';
import { tracePackAdd, tracePackDownload, tracePackPage, tracePackUpdate, tracePackEdit } from '@services/trace-pack';
import { ReformChainError } from '@utils/errorCodeReform';
import { getProductSelectList } from '@services/food';
import { selectBatchList } from '@services/quality-test';
import { getLocalPrivatekey } from '@utils/blockChainUtils';
import { useDispatch } from 'react-redux';
import { decryptedUrl } from '@utils';
import utc from 'dayjs/plugin/utc';
import info from '@assets/icon/info.png';
import su from '@assets/icon/su.png';
import su2 from '@assets/icon/su2.png';
dayjs.extend(utc);

interface IUrlState {
    pageIndex: number;
    pageSize: number;
    // ...
}

const TracePack = (props: any) => {
    const { pageInfo, handlePaginationChange } = props;

    const dispatch = useDispatch();

    const querylist: any = useRef('');

    const [productId, setProductId] = useState('');
    const [visible, setVisible] = useState(false);
    const [codingSuggestionVisible, setCodingSuggestionVisible] = useState(false);

    const [isSimpleSearch, setIsSimpleSearch] = useState(true);

    const [form] = Form.useForm();
    const [addForm] = Form.useForm();
    const navigate = useNavigate();
    const [scanLimitEnabled, setScanLimitEnabled] = useState(false);
    const [securityCodeEnabled, setSecurityCodeEnabled] = useState(false);
    // 判断使用编辑接口
    const [scanEdit, setScanEdit] = useState('编辑码包');
    const [editId, setEditId] = useState('');
    const [editDist, setEditDist] = useState(false);

    //分页数据
    const tracePackQuery: any = useQuery(
        ['tracePackQuery', pageInfo],
        () => {
            return tracePackPage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                productId: querylist?.current?.productId,
                packNumber: querylist?.current?.packNumber?.trim() || undefined,
                productionBatch: querylist?.current?.productionBatch?.trim() || undefined,
                startTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[0]).startOf('day')).format()
                    : undefined,
                endTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[1]).endOf('day')).format()
                    : undefined
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );

    //产品选择(查询)
    const searchSelectProductsQuery = useQuery(
        ['searchSelectProductsQuery'],
        () => {
            return getProductSelectList({
                valid: false
            });
        },
        {
            onSuccess(data) {
                console.log(data);
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );

    //产品选择(add)
    const selectProductsQuery = useQuery(
        ['selectProductsQuery1'],
        () => {
            return getProductSelectList({
                valid: true
            });
        },
        {
            onSuccess(data) {
                console.log(data);
            },
            onError(err: any) {
                ReformChainError(err);
            },
            enabled: !!visible
        }
    );

    // 选择生产批次
    const selectBatchQuery = useQuery(
        ['selectBatchQuery', productId],
        () => {
            return selectBatchList({
                id: productId
            });
        },
        {
            onSuccess(res) {
                // getLocalPrivatekey(dispatch);
            },
            enabled: !!productId
        }
    );

    // 新增溯源码包
    const addTracePack = useMutation(tracePackAdd, {
        onSuccess(res) {
            message.success('新增成功');
            setVisible(false);
            tracePackQuery.refetch();
            addForm.resetFields();
            setScanLimitEnabled(false);
            setSecurityCodeEnabled(false);
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    // 编辑溯源码包
    const editTracePack = useMutation(tracePackEdit, {
        onSuccess(res) {
            message.success('编辑成功');
            setVisible(false);
            tracePackQuery.refetch();
            addForm.resetFields();
            setScanLimitEnabled(false);
            setSecurityCodeEnabled(false);
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    // 禁用启用溯源码包
    const tracePackUpdateMutation = useMutation(tracePackUpdate, {
        onSuccess(res) {
            message.success('操作成功');
            tracePackQuery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });

    console.log(tracePackQuery, 'queryTracePack queryTracePack');

    const columns: ColumnsType<any> = [
        {
            title: '码包ID',
            dataIndex: 'packNumber',
            key: 'packNumber',
            ellipsis: true
        },
        {
            title: '所属产品',
            dataIndex: 'productName',
            key: 'productName',
            ellipsis: true
        },
        {
            title: '生产批次',
            dataIndex: 'productionBatch',
            key: 'productionBatch',
            ellipsis: true
        },
        {
            title: '溯源码数量',
            dataIndex: 'codeCount',
            key: 'codeCount',
            ellipsis: true
        },
        {
            title: '单码扫码限制',
            dataIndex: 'maxQuantity',
            key: 'maxQuantity',
            width: 130,
            ellipsis: true,
            render: (_, row) => <span>{row.maxQuantity ? row.maxQuantity + '次' : '-'}</span>
        },
        {
            title: '创建人',
            dataIndex: 'optName',
            key: 'optName',
            ellipsis: true
        },
        {
            title: '生码时间',
            dataIndex: 'createTime	',
            key: 'createTime',
            ellipsis: true,
            render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')
        },

        {
            title: '可用状态',
            dataIndex: 'state',
            key: 'state',
            ellipsis: true,
            render: (state, row) => <Badge status={state ? 'error' : 'success'} text={state ? '禁用' : '可用'} />
        },
        {
            title: '操作',
            width: 260,
            render: (_, row) => (
                <Space>
                    <BaseButton
                        className={row.state ? 'primaryBtn' : 'warnBtn'}
                        onClick={() => {
                            tracePackUpdateMutation.mutate({
                                id: row?.id,
                                opt: row?.state ? 'ENABLE' : 'DISABLE'
                            });
                        }}
                    >
                        {row?.state ? '启用' : '禁用'}
                    </BaseButton>

                    <BaseButton
                        type='text'
                        className='editBtn'
                        onClick={() => {
                            // console.log('record', record);
                            // const paths = record?.menu;
                            // const staffmenu = paths?.split(',')?.map((item: any) => {
                            //     return staff[item];
                            // });
                            // // console.log("staffmenu",staffmenu)
                            setScanLimitEnabled(row?.maxQuantity > 0);
                            setSecurityCodeEnabled(row?.securityCodeEnabled === true || row?.securityCodeEnabled === 1);

                            addForm.setFieldsValue({
                                productId: row?.productName,
                                productionId: row?.productionBatch,
                                count: row?.codeCount,
                                scanCodeLimitControl: row?.maxQuantity > 0 ? 1 : 0,
                                maxQuantity: row?.maxQuantity > 0 ? row?.maxQuantity : '', // 使用外部变量 maxQuantity
                                securityCodeEnabled:
                                    row?.securityCodeEnabled === true || row?.securityCodeEnabled === 1 ? 1 : 0
                                //
                                // limits: row?.codeCount,
                            });
                            setEditDist(true);
                            setEditId(row?.id);
                            // setEditModelVisible(true);
                            setVisible(true);
                            setScanEdit('编辑码包');
                        }}
                    >
                        编辑
                    </BaseButton>

                    <BaseButton
                        // style={{ borderColor: 'rgba(61, 115, 239, 1)', color: 'rgba(61, 115, 239, 1)' }}
                        className='primaryBtn'
                        onClick={async () => {
                            try {
                                const data: any = await tracePackDownload({ tracePackId: row?.id });
                                console.log(data);
                                var link = document.createElement('a');
                                const result = await decryptedUrl(data.data);

                                console.log(result);

                                link.href = result;
                                link.setAttribute('download', '溯源码包');
                                link.style.display = 'none';
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                            } catch (error: any) {
                                if (error?.data?.message === '码包生成中，请稍后下载') {
                                    message.warning(error?.data?.message);
                                } else {
                                    console.log(error?.data?.message);
                                }
                            }
                        }}
                    >
                        下载码包
                    </BaseButton>
                </Space>
            )
        }
    ];

    const searchFormItems = [
        <Form.Item label='码包ID' name='packNumber'>
            <BaseInput placeholder='请输入码包ID'></BaseInput>
        </Form.Item>,
        <Form.Item label='生产批次' name='productionBatch'>
            <BaseInput placeholder='请输入批次号'></BaseInput>
        </Form.Item>,
        <Form.Item label='所属产品' name='productId'>
            <BaseSelect
                placeholder='请选择'
                options={searchSelectProductsQuery?.data?.data?.map((item: any) => {
                    return {
                        label: item?.productName,
                        value: item?.id
                    };
                })}
            ></BaseSelect>
        </Form.Item>,
        <Form.Item label='生码时间' name='createTime'>
            <BaseDatePicker style={{ width: '100%' }}></BaseDatePicker>
        </Form.Item>
    ];

    return (
        <>
            <Card style={{ marginBottom: 10 }} title={<PageTitle title='溯源码包列表' bg='container su' />}>
                <Form
                    form={form}
                    labelAlign='left'
                    labelCol={{ span: 5 }}
                    onFinish={(values) => {
                        handlePaginationChange(1);
                        querylist.current = values;
                        tracePackQuery.refetch();
                    }}
                    className='label-title'
                >
                    <Row gutter={[36, 12]}>
                        {searchFormItems.slice(0, isSimpleSearch ? 2 : searchFormItems.length).map((searchFormItem) => (
                            <Col key={searchFormItem.key} span={8}>
                                {searchFormItem}
                            </Col>
                        ))}
                        <Col span={isSimpleSearch ? 8 : 16}>
                            <div style={{ display: 'flex', justifyContent: 'end' }}>
                                <Space>
                                    <BaseButton
                                        type='primary'
                                        htmlType='submit'
                                        icon={<SearchOutlined rev={undefined} />}
                                    >
                                        查询
                                    </BaseButton>
                                    <BaseButton
                                        type='dashed'
                                        className='primaryBtn'
                                        icon={<SyncOutlined rev={undefined} />}
                                        onClick={() => {
                                            form.resetFields();
                                            querylist.current = null;
                                            tracePackQuery.refetch();
                                        }}
                                    >
                                        重置
                                    </BaseButton>
                                    <BaseButton
                                        type='link'
                                        style={{ color: '#80a932' }}
                                        onClick={() => {
                                            setIsSimpleSearch(!isSimpleSearch);
                                        }}
                                    >
                                        {isSimpleSearch ? '展开' : '收起'}
                                        {isSimpleSearch ? (
                                            <DownOutlined rev={undefined} />
                                        ) : (
                                            <UpOutlined rev={undefined} />
                                        )}
                                    </BaseButton>
                                </Space>
                            </div>
                        </Col>
                    </Row>
                </Form>
            </Card>
            <BaseCard className={styles.coreFIrmContainer}>
                <div style={{ display: 'flex', justifyContent: 'end' }}>
                    {/* <div
                        className={styles.infoContainer}
                        onClick={() => setCodingSuggestionVisible(true)}
                        style={{ cursor: 'pointer' }}
                    >
                        <img src={info} alt='' />
                        <span>赋码建议</span>
                    </div> */}
                    <BaseButton
                        type='dashed'
                        icon={<PlusOutlined rev={undefined} />}
                        className='bgBtn'
                        onClick={() => {
                            setVisible(true);
                            setEditDist(false);
                            setScanEdit('生成码包');
                            setScanLimitEnabled(false);
                            setSecurityCodeEnabled(false);
                        }}
                        style={{ marginBottom: '20px' }}
                    >
                        新建溯源码包
                    </BaseButton>
                </div>
                <BaseTable
                    loading={tracePackQuery.isFetching}
                    columns={columns}
                    dataSource={tracePackQuery?.data?.data?.records as any}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={tracePackQuery?.data?.data?.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
                <BaseModal
                    title={scanEdit}
                    visible={visible}
                    okHandle={async () => {
                        try {
                            const values = await addForm.validateFields();

                            if (scanEdit == '编辑码包') {
                                editTracePack.mutate({
                                    id: editId,
                                    scanCodeLimitControl: scanLimitEnabled ? 1 : 0,
                                    maxQuantity: values.maxQuantity || 0,
                                    securityCodeEnabled: securityCodeEnabled
                                });
                                return;
                            }
                            addTracePack.mutate({
                                count: values.count,
                                productId: values.productId,
                                productionId: values.productionId,
                                scanCodeLimitControl: scanLimitEnabled ? 1 : 0,
                                maxQuantity: values.maxQuantity || 0,
                                securityCodeEnabled: securityCodeEnabled
                            });
                        } catch {}
                    }}
                    onCancelHandle={() => {
                        setVisible(false);
                        addForm.resetFields();
                        setScanLimitEnabled(false);
                        setSecurityCodeEnabled(false);
                    }}
                >
                    <Form
                        form={addForm}
                        labelCol={{ span: 7 }}
                        wrapperCol={{ span: 20 }}
                        className='edit-label-title tooltip'
                    >
                        <Form.Item
                            label='产品名称'
                            name='productId'
                            rules={[{ required: true, message: '请选择产品!' }]}
                        >
                            {editDist ? (
                                <BaseInput disabled={editDist} placeholder='请选择产品'></BaseInput>
                            ) : (
                                <BaseSelect
                                    placeholder='请选择产品'
                                    onChange={async (option) => {
                                        await addForm.setFieldValue('productionId', '');
                                        setProductId(option);
                                    }}
                                    options={selectProductsQuery?.data?.data?.map((item: any) => {
                                        return {
                                            label: item?.productName,
                                            value: item?.id
                                        };
                                    })}
                                ></BaseSelect>
                            )}
                        </Form.Item>
                        <Form.Item
                            label='生产批次'
                            name='productionId'
                            rules={[{ required: true, message: `${productId ? '请选择生产批次!' : '请先选择产品!'}` }]}
                        >
                            {editDist ? (
                                <BaseInput disabled={editDist} placeholder='请选择生产批次'></BaseInput>
                            ) : (
                                <BaseSelect
                                    disabled={!productId}
                                    placeholder={productId ? '请选择生产批次' : '请先选择产品'}
                                    options={selectBatchQuery?.data?.data?.map((item: any) => {
                                        return {
                                            label: item?.productionBatch,
                                            value: item?.id
                                        };
                                    })}
                                ></BaseSelect>
                            )}
                        </Form.Item>
                        <Form.Item
                            label='溯源码数量'
                            name='count'
                            rules={[{ required: true, message: '请输入溯源码数量!' }]}
                        >
                            <InputNumber
                                disabled={editDist}
                                placeholder='请输入'
                                min={0}
                                max={999999}
                                precision={0}
                            ></InputNumber>
                        </Form.Item>

                        <Form.Item
                            label='扫码次数限制控制'
                            name='scanCodeLimitControl'
                            // rules={[{ required: true, message: '请选择扫码次数限制控制!' }]}
                            initialValue={0}
                        >
                            <Switch
                                checked={scanLimitEnabled}
                                onChange={(checked) => {
                                    setScanLimitEnabled(checked);
                                    addForm.setFieldValue('scanCodeLimitControl', checked ? 1 : 0);
                                }}
                            />
                            &nbsp;
                            <span style={{ fontSize: 10 }}>
                                开启后，若二维码扫描次数超过次数限制，系统将进行风险提示
                            </span>
                        </Form.Item>

                        {scanLimitEnabled && (
                            <Form.Item
                                label='单码扫码限制'
                                name='maxQuantity'
                                tooltip={{
                                    title: (
                                        <div>
                                            温馨提示：单码扫码次数限制设置指南
                                            <ul>
                                                <li>①一物一码产品：</li>
                                                <li>
                                                    对于每个产品对应唯一溯源码（即溯源码数量≥当前批次生产数量）的情况，请设置一个二维码扫描次数的限制。当扫描次数超过您设定的限制值时，系统将向消费者提示可能存在安全风险。
                                                </li>

                                                <li>②一批一码或一码多物产品：</li>
                                                <li>
                                                    对于多个产品共享同一个溯源码（即溯源码数量&lt;当前批次生产数量）的情况，建议将扫码限制设置为当前批次生产数量的10倍。当二维码扫码次数超过此值时，系统将提示消费者可能存在安全风险。
                                                </li>
                                            </ul>
                                            <br />
                                            请根据产品类型和生产批次合理设置扫码次数限制，以确保产品溯源的安全性和可靠性。
                                        </div>
                                    ),
                                    overlayInnerStyle: { width: '490px' }
                                }}
                                rules={[{ required: true, message: '请输入单码扫码限制!' }]}
                            >
                                <InputNumber placeholder='请输入' min={1} max={9999999} precision={0} />
                            </Form.Item>
                        )}

                        {
                            <Form.Item
                                label='双码防伪'
                                name='securityCodeEnabled'
                                tooltip={{
                                    title: (
                                        <div style={{ display: 'flex' }}>
                                            <div
                                                style={{
                                                    width: '190px',
                                                    textAlign: 'center',
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    alignItems: 'center'
                                                }}
                                            >
                                                <div style={{ width: '190px' }}>未开启</div>
                                                <img
                                                    src={su}
                                                    alt=''
                                                    style={{ width: '160px', height: '180px', margin: '10px auto' }}
                                                />
                                            </div>

                                            <div
                                                style={{
                                                    width: '190px',
                                                    textAlign: 'center',
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    alignItems: 'center'
                                                }}
                                            >
                                                <div style={{ width: '190px' }}>开启后</div>
                                                <img
                                                    src={su2}
                                                    alt=''
                                                    style={{ width: '160px', height: '180px', margin: '10px auto' }}
                                                />
                                            </div>
                                        </div>
                                    ),
                                    overlayInnerStyle: { width: '400px' }
                                }}
                            >
                                <Switch
                                    checked={securityCodeEnabled}
                                    onChange={(checked) => {
                                        setSecurityCodeEnabled(checked);
                                        addForm.setFieldValue('securityCodeEnabled', checked ? 1 : 0);
                                    }}
                                />
                                &nbsp;
                                <span style={{ fontSize: 10 }}>
                                    开启后，扫码需刮开涂层输入防伪码，双重验证确保产品真伪
                                </span>
                            </Form.Item>
                        }
                    </Form>
                </BaseModal>

                {/* 赋码建议弹框 */}
                <BaseModal
                    title='赋码建议'
                    visible={codingSuggestionVisible}
                    onCancelHandle={() => setCodingSuggestionVisible(false)}
                    okHandle={() => setCodingSuggestionVisible(false)}
                    okText='确定'
                    hidden={true}
                    width={600}
                >
                    <div style={{ padding: '20px 0' }}>
                        <div style={{ marginBottom: '24px' }}>
                            <h5 style={{ marginBottom: '8px' }}>1、一物一码产品​ </h5>
                            <p style={{ marginBottom: '8px', lineHeight: '1.6' }}>
                                采用唯一编码绑定单件产品，通过哈希算法生成不可篡改的溯源码，记录生产、物流、销售全流程数据。适用于高价值、需精准追溯的商品（如奢侈品、电子产品），消费者扫码可查产品全生命周期信息，实现精准防伪与追溯。
                            </p>
                        </div>

                        <div style={{ marginBottom: '24px' }}>
                            <h5 style={{ marginBottom: '8px' }}> 2、一批一码或一码多物产品​​ </h5>
                            <p style={{ marginBottom: '8px', lineHeight: '1.6' }}>
                                为同一批次或多个同类产品分配统一编码，关联批次内产品共性信息（如生产日期、批次号、质检报告）。适用于快消品、农产品等，降低赋码成本与管理复杂度，扫码可获取批次整体溯源数据，兼顾效率与监管需求。
                            </p>
                        </div>
                    </div>
                </BaseModal>
            </BaseCard>
        </>
    );
};

export default WithPaginate(TracePack);
