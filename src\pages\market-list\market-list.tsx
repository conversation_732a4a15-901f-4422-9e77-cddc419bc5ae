/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-09 16:17:21
 * @LastEditTime: 2022-10-14 10:15:21
 * @LastEditors: PhilRandWu
 */
/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:24:01
 * @LastEditTime: 2022-10-09 16:16:29
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Badge, Form, message, Space, Button, Row, Col, DatePicker } from 'antd';
import { foodPage, FoodState } from '@services/food';
import useUrlState from '@ahooksjs/use-url-state';
import { useMutation, useQuery } from 'react-query';
import styles from './index.module.less';
import { useRef, useState } from 'react';
import PageTitle from '@components/page-title';
import { SearchOutlined, PlusOutlined, SyncOutlined } from '@ant-design/icons';
import TableHead from '@components/table-head';
import BaseButton from '@components/base-button';
import { useFoodList } from '../../myhooks/usefoodlist';
import copyToClipboard from 'copy-to-clipboard';
import BaseInput from '@components/base-input';
import SingleSearch from '@components/single-search';
import { Navigate, useNavigate } from 'react-router-dom';
import WithPaginate from '../../hoc/withpaginate';
import { ReformChainError } from '@utils/errorCodeReform';
import { ColumnsType } from 'antd/lib/table';
import BaseSelect from '@components/base-select';
import { landPlantPage, LandSourceService, LandSourceServiceUser, salesList, salesExport } from '@services/land-test';
import { decryptedUrl } from '@utils';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);
const FoodList = (props: any) => {
    const { pageInfo, handlePaginationChange } = props;
    const navigate = useNavigate();
    const [addEmployeesForm] = Form.useForm();
    const [editEmployeesForm] = Form.useForm();
    const querylist = useRef<any>('');
    const [search]: any = Form.useForm();
    const queryList: any = useFoodList({
        pageIndex: pageInfo.pageIndex,
        pageSize: pageInfo.pageSize
    });
    const { RangePicker } = DatePicker;
    console.log('queryList', queryList);
    //修改状态
    // const foodstate = useMutation(FoodState, {
    //     onSuccess(res) {
    //         message.success('修改状态成功');
    //         foodquery.refetch();
    //     },
    //     onError(err: any) {
    //         ReformChainError(err);
    //         foodquery.refetch();
    //     }
    // });
    const foodquery = useQuery(
        ['foodquery', pageInfo],
        () => {
            // if (!userInfo.user?.organization_id) {
            //     message.error('未获取到机构id');
            //     return Promise.reject('未获取到机构id');
            // }
            console.log(querylist?.current?.startTime);
            return salesList({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                // param: querylist?.current,
                orderId: querylist?.current?.landName, //订单编号
                finishBeginTime: querylist?.current?.startTime
                    ? dayjs.utc(dayjs(querylist?.current?.startTime[0]).startOf('day')).format()
                    : undefined,
                finishEndTime: querylist?.current?.startTime
                    ? dayjs.utc(dayjs(querylist?.current?.startTime[1]).endOf('day')).format()
                    : undefined,
                spuName: querylist?.current?.farmerName // 产品名称
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    console.log('staffquery', foodquery);

    const landProductSeleUser = useQuery(['landProductSeleUser'], () => LandSourceServiceUser());

    const landProductSeleUserData: any[] = landProductSeleUser?.data?.data;
    const tableData = foodquery?.data?.data?.records?.map((item: any) => ({
        ...item,
        finishTime: dayjs(item.finishTime).format('YYYY-MM-DD HH:mm:ss')
    }));
    const listColumn: ColumnsType<any> = [
        {
            title: '渠道名称',
            dataIndex: 'channelName',
            key: 'channelName',
            ellipsis: true
        },
        {
            title: '订单编号',
            dataIndex: 'orderId',
            key: 'name',
            ellipsis: true
        },
        {
            title: '产品名称',
            dataIndex: 'spuName',
            key: 'code',
            ellipsis: true
        },
        // {
        //     title: '销售单价(元)',
        //     dataIndex: 'primiPrice',
        //     key: 'type',
        //     ellipsis: true
        // },
        {
            title: '数量',
            dataIndex: 'qtySku',
            key: 'type',
            ellipsis: true
        },
        // {
        //     title: '销售总价(元)',
        //     dataIndex: 'finalPrice',
        //     key: 'type',
        //     ellipsis: true
        // },
        {
            title: '发货/出库时间',
            dataIndex: 'finishTime',
            key: 'type',
            ellipsis: true
        },

        // {
        //     title: '状态',
        //     dataIndex: 'state',
        //     key: 'state',
        //     ellipsis: true,
        //     render: (data: any) => (
        //         <span style={{ color: data ? '#F64041' : '#666666' }}>
        //             <Badge
        //                 status={data ? 'error' : 'success'}
        //                 color={data ? '#F64041' : 'rgb(36, 171, 59)'}
        //                 text={data ? '禁用' : '可用'}
        //             />
        //         </span>
        //     )
        // },
        {
            width: 210,
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='middle' className='operation'>
                    {/* <BaseButton
                        // type='dashed'
                        className={record.state ? 'primaryBtn' : 'warnBtn'}
                        // ghost
                        onClick={() => {
                            // console.log("record",record)
                            const opp = foodstate.mutate({
                                opt: record?.state ? 'ENABLE' : 'DISABLE',
                                id: record?.id
                            });
                        }}
                    >
                        {record.state ? '启用' : '禁用'}
                    </BaseButton>
                    <BaseButton
                        type='text'
                        className='editBtn'
                        onClick={() => {
                            const foodid = record?.id;
                            navigate('edit', {
                                state: {
                                    id: foodid
                                }
                            });
                        }}
                    >
                        编辑
                    </BaseButton> */}
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            const foodid = record?.id;
                            navigate('detail', {
                                state: {
                                    id: foodid
                                }
                            });
                        }}
                    >
                        查看详情
                    </BaseButton>
                </Space>
            )
        }
    ];

    // const searchConfig = {
    //     label: '',
    //     classname: 'searchConfig-input',
    //     handleSearch: (values: any) => {
    //         handlePaginationChange(1);
    //         foodquery.refetch();
    //     },
    //     placeholder: '输入产品编号/产品名称',
    //     setSearchValue: (values: any) => {
    //         // console.log("values",values)
    //         querylist.current = values;
    //     }
    // };
    const exportFile = async () => {
        // 导出函数
        if (tableData.length == 0) {
            return message.warning('当前暂无可导出数据');
        }
        try {
            const data: any = await salesExport({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                // param: querylist?.current,
                orderId: querylist?.current?.landName, //订单编号
                finishBeginTime: querylist?.current?.startTime
                    ? dayjs.utc(dayjs(querylist?.current?.startTime[0]).startOf('day')).format()
                    : undefined,
                finishEndTime: querylist?.current?.startTime
                    ? dayjs.utc(dayjs(querylist?.current?.startTime[1]).endOf('day')).format()
                    : undefined,
                spuName: querylist?.current?.farmerName //商品名称
            });
            console.log(data);
            var link = document.createElement('a');
            // debugger

            // var blob = new Blob([data], {
            //  type: "text/xml" });
            //  debugger
            //创建指向内存中字节流的链接
            link.href = URL.createObjectURL(data);
            link.setAttribute('download', '销售信息列表.xlsx');
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } catch (error: any) {
            if (error?.data?.message === '生成中，请稍后下载') {
                message.warning(error?.data?.message);
            } else {
                console.log(error?.data?.message);
            }
        }
    };

    return (
        <>
            <BaseCard
                // className="coreFIrmContainer"
                className={styles.coreFIrmContainer}
                // mt24
                title={<PageTitle title='销售管理列表' bg='container xiao' />}
            >
                <div
                // style={{
                //     display: 'flex',
                //     width: '100%'
                // }}
                >
                    {/* <SingleSearch {...searchConfig} /> */}
                    <Form
                        onFinish={(values) => {
                            handlePaginationChange(1);
                            console.log('values', values);
                            querylist.current = values;
                            foodquery.refetch();
                        }}
                        form={search}
                        // labelCol={{ span: 6 }}
                        className='label-title'
                    >
                        {/* <SearchForm /> */}
                        <Row
                            gutter={[24, 0]}
                            style={{
                                width: '100%'
                            }}
                        >
                            {/* <Col span={6}>
                  <Form.Item label='生产批次' name='productionBatch'>
                      <BaseInput placeholder='请输入生产批次'></BaseInput>
                  </Form.Item>
              </Col> */}
                            {/* <Col span={6}>
                  <Form.Item label='创建时间' name='createTime'>
                      <BaseDatePicker style={{ width: '100%' }}></BaseDatePicker>
                  </Form.Item>
              </Col> */}
                            {/* <Col span={6}>
                  <Form.Item label='质检结果' name='inspectionResults'>
                      <BaseSelect
                          placeholder='请选择'
                          options={[
                              { label: '合格', value: 1 },
                              { label: '不合格', value: 2 }
                          ]}
                      ></BaseSelect>
                  </Form.Item>
              </Col> */}
                            <Col span={6}>
                                <Form.Item label='订单编号' name='landName'>
                                    <BaseInput placeholder='请输入订单编号'></BaseInput>
                                    {/* <BaseSelect
                    placeholder='请输入订单编号'
                  // options={landProductListData?.map((item) => ({
                  //     label: item?.landName,
                  //     value: item?.landId
                  // }))}
                  ></BaseSelect> */}
                                </Form.Item>
                            </Col>
                            <Col span={7}>
                                <Form.Item label='发货/出库时间' name='startTime'>
                                    <RangePicker
                                        style={{ width: '100%' }}
                                        // getPopupContainer={(trigger: any) => trigger.parentNode}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={6}>
                                <Form.Item label='产品名称' name='farmerName'>
                                    <BaseInput style={{ width: '90%' }} placeholder='请输入产品名称'></BaseInput>
                                    {/* <BaseSelect
                                        placeholder='请选择产品名称'
                                        options={landProductSeleUserData?.map((item) => ({
                                            label: item?.farmerName,
                                            value: item?.farmerId
                                        }))}
                                    ></BaseSelect> */}
                                </Form.Item>
                            </Col>
                            <Col span={6}>
                                <div
                                    style={{
                                        display: 'flex',
                                        justifyContent: 'end',
                                        marginLeft: 10
                                    }}
                                >
                                    <Space>
                                        <BaseButton
                                            htmlType='submit'
                                            type='primary'
                                            // className='searchBtn'
                                            // style={{width: 100}}
                                            className={`${styles.searchBtn} ${styles.baseBtn}`}
                                            icon={<SearchOutlined rev={undefined} />}
                                        >
                                            查询
                                        </BaseButton>
                                        <BaseButton
                                            type='dashed'
                                            className='primaryBtn'
                                            // style={{width: 100}}
                                            icon={<SyncOutlined rev={undefined} />}
                                            onClick={() => {
                                                querylist.current = null;
                                                foodquery.refetch();
                                                search.resetFields();
                                            }}
                                        >
                                            重置
                                        </BaseButton>

                                        <BaseButton
                                            type='dashed'
                                            className='primaryBtn'
                                            style={{ marginLeft: 50 }}
                                            // icon={<SyncOutlined rev={undefined} />}
                                            // onClick={() => {
                                            //   // querylist.current = null;
                                            //   // qualityquery.refetch();
                                            //   foodquery.refetch();

                                            //   search.resetFields();
                                            // }}
                                            onClick={exportFile}
                                        >
                                            导出
                                        </BaseButton>
                                        {/* <BaseButton
                                            type='dashed'
                                            icon={<PlusOutlined rev={undefined} />}
                                            className='greenBtn'
                                            onClick={() => {
                                                navigate('/qcqa/list/add');
                                            }}
                                        >
                                            新建质检
                                        </BaseButton> */}
                                    </Space>
                                </div>
                            </Col>
                            {/* <Col span={4}>
                                <div style={{ textAlign: 'right' }}>
                                    <BaseButton
                                        type='dashed'
                                        icon={<PlusOutlined rev={undefined} />}
                                        className='bgBtn'
                                        onClick={() => {
                                            navigate('add');
                                        }}
                                    >
                                        新建收购
                                    </BaseButton>
                                </div>
                            </Col> */}
                        </Row>
                    </Form>
                    {/* <div style={{ marginBottom: '15px', textAlign: 'right' }}>
                        <BaseButton
                            type='dashed'
                            className='primaryBtn'
                            // style={{width: 100}}
                            // icon={<SyncOutlined rev={undefined} />}
                            // onClick={() => {
                            //   // querylist.current = null;
                            //   // qualityquery.refetch();
                            //   foodquery.refetch();

                            //   search.resetFields();
                            // }}
                            onClick={exportFile}
                        >
                            导出
                        </BaseButton>
                    </div> */}
                </div>
                <BaseTable
                    rowKey='account'
                    className='food-table-operation'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return <TableHead />;
                    }}
                    columns={listColumn}
                    dataSource={tableData}
                    loading={foodquery?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={foodquery?.data?.data?.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>
        </>
    );
};

export default WithPaginate(FoodList);
