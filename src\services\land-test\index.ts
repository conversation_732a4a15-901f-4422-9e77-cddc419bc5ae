import request from '../request';
//种植分页
export const landPage = (obj: any) => {
    return request({
        url: '/land/getList',
        method: 'post',
        data: obj
    });
};
//详情列表

export const landTestDetail = (obj: any) => {
  // console.log("obj",obj)
  return request({
      url: '/LandPlantBatch/getList',
      method: 'post',
      data: obj
  });
};
// 编辑
export const landDeit = (obj: any) => {
  // console.log("obj",obj)
  return request({
      url: '/LandPlantBatch/modify-state',
      method: 'post',
      data: obj
  });
};
// 详情

export const landDetails = (obj: any) => {
  return request({
      url: `/land/getDetail?landId=`+obj,
      method: 'get',
      // params: obj
  });
};




//新增
export const addQuality = (obj: any) => {
    return request({
        url: '/quality/add',
        method: 'post',
        data: obj
    });
};
//产品列表
export const selectProductList = (obj: any) => {
    return request({
        url: '/product/list',
        method: 'get',
        params: obj
    });
};
//生产列表
export const selectBatchList = (obj: any) => {
    return request({
        url: '/production/list',
        method: 'post',
        data: obj
    });
};


// 地块名称

export const LandSourceService = () => {
  return request({
      url: '/land/getLandNames',
      method: 'get',
      // params: obj
  });
};
// 农户名称
export const LandSourceServiceUser = () => {
  return request({
      url: '/land/getFarmerNames',
      method: 'get',
      // params: obj
  });
};


// 农作物类型

export const PlantNamesSourceService = () => {
  return request({
      url: '/LandPlantBatch/getPlantNames',
      method: 'get',
      // params: obj
  });
};

// 种植溯源列表

export const landPlantPage = (obj: any) => {
  return request({
      url: '/production/landPlantPage',
      method: 'post',
      data: obj
  });
};

// 种植溯源种植户


export const FarmerNameById = (obj: any) => {
  return request({
      url: `/LandPlantBatch/getFarmerNameById?farmerName=`+obj,
      method: 'get',
      // params: obj
  });
};

// 销售管理列表
export const salesList = (obj: any) => {
  return request({
      url: '/sales/list',
      method: 'post',
      data: obj
  });
};
// 销售管理查看详情
export const getSalesDetail = (obj: any) => {
  return request({
      url: `/sales/`+`${obj.productId}`+`/list`,
      method: 'get',
  });
};
// 销售管理导出
export const salesExport = (obj: any) => {
  return request({
      url: 'sales/export',
      method: 'post',
      data: obj,
      responseType:'blob',
  });
};

