/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 17:15:57
 * @LastEditTime: 2022-11-01 18:12:36
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BaseFormItem from '@components/base-form-item';
import PageTitle from '@components/page-title';
import BaseTable from '@components/base-table';
import BasePagination from '@components/base-pagination';
import TableHead from '@components/table-head';
import React, { useState } from 'react';
import { Form, message, Table } from 'antd';
import { useMutation, useQuery } from 'react-query';
import { foodDetail } from '@services/food';
import { enumeration } from './config';
import SwitchList from '@components/switch-list';
import styles from './index.module.less';
import { useLocation } from 'react-router-dom';
import { ReformChainError } from '@utils/errorCodeReform';
import dayjs from 'dayjs';
import { Image } from 'antd';
import FilterForm from '@components/filter-form';

import { FormItemImages, FormItemVideo } from '@components';
import ChainDetailModal from '@components/chain_detail_modal';
import { decryptedUrl, isArrayArr } from '@utils';
import { getSalesDetail } from '@services/land-test';
import { useAppSelector } from '@store';
import { RoleEnum } from '@config';

const FoodDetail = (props: any) => {
    const { state } = useLocation();
    const [detailForm] = Form.useForm();
    const [detailsForm] = Form.useForm();
    const [channelInformation] = Form.useForm();
    const [basicForm] = Form.useForm();
    const [returnForm] = Form.useForm();
    const [marketForm] = Form.useForm();

    const [chainForm] = Form.useForm();

    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);
    // 分页状态
    const [productPageInfo, setProductPageInfo] = useState({ pageIndex: 1, pageSize: 5 });
    const [logisticsPageInfo, setLogisticsPageInfo] = useState({ pageIndex: 1, pageSize: 5 });
    // console.log("state",state)

    const detailquery = useQuery(
        ['detailquery9999'],
        () => {
            return getSalesDetail({
                productId: state?.id
            });
        },
        {
            async onSuccess(res) {
                console.log('res0990099', res);
                const arrayData = await Promise.all(
                    isArrayArr(res?.data?.productImg)?.map((item: any) => {
                        return decryptedUrl(item);
                    })
                );
                // setPromotionPicData(arrayData)
                // const videos = await decryptedUrl(res?.data?.productVideo);
                // detailForm.setFieldsValue({
                //   ...res.data,
                //   productImg: arrayData && arrayData.length > 0 ? arrayData : null,
                //   productVideo: videos ? videos : null,
                //   transactionTime: res?.data?.transactionTime
                //     ? dayjs(res?.data?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
                //     : '-'
                // });
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    console.log('detailquery', detailquery?.data?.data);
    const fooddetail = detailquery?.data?.data?.food;

    const basicProductData = detailquery?.data?.data?.basicProductData;
    const DetailData = detailquery?.data?.data?.orderDetailData;
    const ReturnData = detailquery?.data?.data?.ProductReturnData;
    const chainData = detailquery?.data?.data?.chainData;

    // 获取用户角色信息
    const userInfo = useAppSelector((store) => store.user);
    const LocalLoginIdentity = Number(userInfo?.userInfo?.identity);

    // 获取渠道名称并判断是否为"本来生活"
    const channelName = DetailData?.channelName || '';
    const isBenLaiShengHuo = channelName === '本来生活';

    // 判断是否为销售企业且渠道为本来生活
    const isSalesEnterpriseAndBenLai = LocalLoginIdentity === RoleEnum.销售企业 && isBenLaiShengHuo;

    // 模拟产品基本信息表格数据（实际应从API获取）
    const productTableData = [
        {
            key: '1',
            packageCode: 'PKG001',
            supplierName: '绿色农业有限公司',
            supplierCode: 'SUP001',
            productName: '有机大米',
            channelProductCode: 'CH001',
            productQuantity: '10kg',
            productCategory: '粮食',
            specification: '5kg/袋'
        },
        {
            key: '2',
            packageCode: 'PKG002',
            supplierName: '优质农产品公司',
            supplierCode: 'SUP002',
            productName: '有机小麦',
            channelProductCode: 'CH002',
            productQuantity: '8kg',
            productCategory: '粮食',
            specification: '2kg/袋'
        }
        {
            key: '3',
            packageCode: 'PKG003',
            supplierName: '生态农场',
            supplierCode: 'SUP003',
            productName: '有机玉米',
            channelProductCode: 'CH003',
            productQuantity: '12kg',
            productCategory: '粮食',
            specification: '3kg/袋'
        },
        {
            key: '4',
            packageCode: 'PKG004',
            supplierName: '天然食品公司',
            supplierCode: 'SUP004',
            productName: '有机蔬菜',
            channelProductCode: 'CH004',
            productQuantity: '5kg',
            productCategory: '蔬菜',
            specification: '1kg/袋'
        },
        {
            key: '5',
            packageCode: 'PKG005',
            supplierName: '健康农业',
            supplierCode: 'SUP005',
            productName: '有机水果',
            channelProductCode: 'CH005',
            productQuantity: '8kg',
            productCategory: '水果',
            specification: '2kg/盒'
        },
        {
            key: '6',
            packageCode: 'PKG006',
            supplierName: '绿色农业有限公司',
            supplierCode: 'SUP001',
            productName: '有机豆类',
            channelProductCode: 'CH006',
            productQuantity: '6kg',
            productCategory: '豆类',
            specification: '1kg/袋'
        }
        // 更多数据...
    ];

    // 模拟物流信息表格数据（实际应从API获取）
    const logisticsTableData = [
        {
            key: '1',
            packageNumber: 'PKG001',
            outboundTime: '2024-01-15 10:30:00',
            trackingNumber: 'SF1234567890',
            logisticsCompany: '顺丰速运',
            deliveryMethod: '标准快递'
        },
        {
            key: '2',
            packageNumber: 'PKG002',
            outboundTime: '2024-01-15 11:00:00',
            trackingNumber: 'YT9876543210',
            logisticsCompany: '圆通速递',
            deliveryMethod: '经济快递'
        }
        // 更多数据...
    ];

    // 分页处理函数
    const handleProductPaginationChange = (pageIndex: number, pageSize?: number) => {
        setProductPageInfo({
            pageIndex,
            pageSize: pageSize || productPageInfo.pageSize
        });
    };

    const handleLogisticsPaginationChange = (pageIndex: number, pageSize?: number) => {
        setLogisticsPageInfo({
            pageIndex,
            pageSize: pageSize || logisticsPageInfo.pageSize
        });
    };

    // 渠道信息
    const foodInfoConfig = [
        {
            label: '渠道名称',
            name: 'channelName',
            value: 'channelName',
            type: 'ShowText'
        }
    ];
    // 订单详情信息 - 根据渠道名称和用户角色显示不同字段
    const productionConfig = isBenLaiShengHuo
        ? [
              // 本来生活渠道显示的字段
              {
                  label: '订单编号',
                  name: 'orderId',
                  value: 'orderId',
                  type: 'ShowText',
                  span: 8
              },
              {
                  label: '销售数量',
                  name: 'qtySku',
                  value: 'qtySku',
                  type: 'ShowText',
                  span: 8
              },
              {
                  label: '包裹数量',
                  name: 'packageCount',
                  value: 'packageCount',
                  type: 'ShowText',
                  span: 8
              },
              // 如果是销售企业且渠道为本来生活，不显示供应商信息
              ...(isSalesEnterpriseAndBenLai
                  ? []
                  : [
                        {
                            label: '供应商名称',
                            name: 'supplierName',
                            value: 'supplierName',
                            type: 'ShowText',
                            span: 8
                        },
                        {
                            label: '供应商编码',
                            name: 'supplierId',
                            value: 'supplierId',
                            type: 'ShowText',
                            span: 8
                        }
                    ]),
              {
                  label: '下单时间',
                  name: 'orderTime',
                  value: 'orderTime',
                  type: 'ShowText',
                  span: 8
              }
          ]
        : [
              // 其他渠道显示的原有字段
              {
                  label: '订单编号',
                  name: 'orderId',
                  value: 'orderId',
                  type: 'ShowText',
                  span: 8
              },
              {
                  label: '购买数量',
                  name: 'qtySku',
                  value: 'qtySku',
                  type: 'ShowText',
                  span: 8
              },
              {
                  label: '包裹编号',
                  name: 'packageId',
                  value: 'packageId',
                  type: 'ShowText',
                  span: 8
              },
              {
                  label: '供应商SKU编号',
                  name: 'supplierSkuCode',
                  value: 'supplierSkuCode',
                  type: 'ShowText',
                  span: 8
              },
              {
                  label: '供应商名称',
                  name: 'supplierName',
                  value: 'supplierName',
                  type: 'ShowText',
                  span: 8
              },
              {
                  label: '供应商编码',
                  name: 'supplierId',
                  value: 'supplierId',
                  type: 'ShowText',
                  span: 8
              },
              {
                  label: '销售总价',
                  name: 'finalPrice',
                  value: 'finalPrice',
                  type: 'ShowText',
                  span: 8
              },
              {
                  label: '下单时间',
                  name: 'orderTime',
                  value: 'orderTime',
                  type: 'ShowText',
                  span: 8
              },
              {
                  label: '完成时间',
                  name: 'finishTime',
                  value: 'finishTime',
                  type: 'ShowText',
                  span: 8
              }
          ];
    // 产品基本信息
    const basicInformation = [
        {
            label: '商品名称',
            name: 'spuName',
            value: 'spuName',
            type: 'ShowText',
            span: 8
        },
        {
            label: '产品品类',
            name: 'productCategory',
            value: 'productCategory',
            type: 'ShowText',
            span: 8
        },
        {
            label: '规格',
            name: 'skuSpecs',
            value: 'skuSpecs',
            type: 'ShowText',
            span: 8
        },
        {
            label: '品牌',
            name: 'brandId',
            value: 'brandId',
            type: 'ShowText',
            span: 8
        },
        {
            label: '销售单价',
            name: 'primiPrice',
            value: 'primiPrice',
            type: 'ShowText',
            span: 8
        },
        {
            label: '渠道产品编码',
            name: 'skuId',
            value: 'skuId',
            type: 'ShowText',
            span: 8
        }
    ];

    // 本来生活渠道的产品基本信息表格列定义
    const productTableColumns = [
        {
            title: '包裹编码',
            dataIndex: 'packageCode',
            key: 'packageCode',
            ellipsis: true
        },
        {
            title: '供应商名称',
            dataIndex: 'supplierName',
            key: 'supplierName',
            ellipsis: true
        },
        {
            title: '供应商编号',
            dataIndex: 'supplierCode',
            key: 'supplierCode',
            ellipsis: true
        },
        {
            title: '产品名称',
            dataIndex: 'productName',
            key: 'productName',
            ellipsis: true
        },
        {
            title: '渠道产品编号',
            dataIndex: 'channelProductCode',
            key: 'channelProductCode',
            ellipsis: true
        },
        {
            title: '产品数量',
            dataIndex: 'productQuantity',
            key: 'productQuantity',
            ellipsis: true
        },
        {
            title: '产品品类',
            dataIndex: 'productCategory',
            key: 'productCategory',
            ellipsis: true
        },
        {
            title: '规格',
            dataIndex: 'specification',
            key: 'specification',
            ellipsis: true
        }
    ];

    // 本来生活渠道的物流信息表格列定义
    const logisticsTableColumns = [
        {
            title: '包裹编号',
            dataIndex: 'packageNumber',
            key: 'packageNumber',
            ellipsis: true
        },
        {
            title: '出库时间',
            dataIndex: 'outboundTime',
            key: 'outboundTime',
            ellipsis: true
        },
        {
            title: '运单号',
            dataIndex: 'trackingNumber',
            key: 'trackingNumber',
            ellipsis: true
        },
        {
            title: '物流公司名称',
            dataIndex: 'logisticsCompany',
            key: 'logisticsCompany',
            ellipsis: true
        },
        {
            title: '物流配送方式',
            dataIndex: 'deliveryMethod',
            key: 'deliveryMethod',
            ellipsis: true
        }
    ];

    const marketformation = [
        {
            label: '发货时间',
            name: 'skuNum',
            value: 'skuNum',
            type: 'ShowText',
            span: 8
        },
        {
            label: '物流单号',
            name: 'refundAmount',
            value: 'refundAmount',
            type: 'ShowText',
            span: 8
        },
        {
            label: '物流公司',
            name: 'applyTime',
            value: 'applyTime',
            type: 'ShowText',
            span: 8
        },
        {
            label: '运输方式',
            name: 'channelPayTime',
            value: 'channelPayTime',
            type: 'ShowText',
            span: 8
        }
    ];
    // 产品退货信息
    const returnInformation = [
        {
            label: '售后数量',
            name: 'skuNum',
            value: 'skuNum',
            type: 'ShowText',
            span: 8
        },
        {
            label: '退款金额',
            name: 'refundAmount',
            value: 'refundAmount',
            type: 'ShowText',
            span: 8
        },
        {
            label: '申请时间',
            name: 'applyTime',
            value: 'applyTime',
            type: 'ShowText',
            span: 8
        },
        {
            label: '退款完成时间',
            name: 'channelPayTime',
            value: 'channelPayTime',
            type: 'ShowText',
            span: 8
        },
        {
            label: '售后类型',
            name: 'postsaleType',
            value: 'postsaleType',
            type: 'ShowText',
            span: 8
        }
    ];
    const chainConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: 'transactionId',
            title: '信息的链上的哈希值',
            type: 'Link',
            onClick: () => {
                setChainDetailModalVisible(true);
            }
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: 'transactionTime',
            title: '信息上链的时间',
            type: 'ShowText'
        }
    ];
    chainForm.setFieldsValue({
        transactionId: chainData?.transactionId,
        transactionTime: chainData?.transactionTime
            ? dayjs(chainData?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
            : '-'
    });
    //渠道信息
    channelInformation.setFieldsValue({
        channelName: detailquery?.data?.data?.channelName
    });
    // 订单详情信息
    detailsForm.setFieldsValue({
        orderId: DetailData?.orderId,
        qtySku: DetailData?.qtySku,
        packageId: DetailData?.packageId,
        supplierSkuCode: DetailData?.supplierSkuCode,
        supplierName: DetailData?.supplierName,
        supplierId: DetailData?.supplierId,
        finalPrice: DetailData?.finalPrice,
        orderTime: DetailData?.orderTime ? dayjs(DetailData?.orderTime).format('YYYY-MM-DD HH:mm:ss') : '-',
        finishTime: DetailData?.finishTime ? dayjs(DetailData?.finishTime).format('YYYY-MM-DD HH:mm:ss') : '-'
    });
    // 产品基本信息
    basicForm.setFieldsValue({
        spuName: basicProductData?.spuName,
        productCategory: basicProductData?.productCategory,
        skuSpecs: basicProductData?.skuSpecs ? JSON.parse(basicProductData?.skuSpecs).spec['重量'] : '-',
        brandId: basicProductData?.brandId,
        primiPrice: basicProductData?.primiPrice,
        skuId: basicProductData?.skuId
    });
    // 物流信息
    marketForm.setFieldsValue({
        skuNum: ReturnData?.skuNum ? ReturnData?.skuNum : '暂无',
        refundAmount: ReturnData?.refundAmount ? ReturnData?.refundAmount : '暂无',
        applyTime: ReturnData?.applyTime ? dayjs(ReturnData?.applyTime).format('YYYY-MM-DD HH:mm:ss') : '暂无',
        channelPayTime: ReturnData?.channelPayTime
            ? dayjs(ReturnData?.channelPayTime).format('YYYY-MM-DD HH:mm:ss')
            : '暂无',
        postsaleType: ReturnData?.postsaleType ? ReturnData?.postsaleType : '暂无'
    });
    // 产品退货信息
    returnForm.setFieldsValue({
        skuNum: ReturnData?.skuNum ? ReturnData?.skuNum : '暂无',
        refundAmount: ReturnData?.refundAmount ? ReturnData?.refundAmount : '暂无',
        applyTime: ReturnData?.applyTime ? dayjs(ReturnData?.applyTime).format('YYYY-MM-DD HH:mm:ss') : '暂无',
        channelPayTime: ReturnData?.channelPayTime
            ? dayjs(ReturnData?.channelPayTime).format('YYYY-MM-DD HH:mm:ss')
            : '暂无',
        postsaleType: ReturnData?.postsaleType ? ReturnData?.postsaleType : '暂无'
    });

    const onFinish = (values: any) => {
        console.log('Success:', values);
    };

    const onFinishFailed = (errorInfo: any) => {
        console.log('Failed:', errorInfo);
    };
    console.log('detailquery?.data?.data?.material', detailquery?.data?.data?.material);
    return (
        <div>
            <BaseCard title={<PageTitle title='销售信息详情' bg='container chan' />}>
                <Form form={channelInformation}>
                    <PageTitle title='渠道信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={foodInfoConfig} labelCol={false} />
                </Form>
                <Form form={detailsForm}>
                    <PageTitle title='订单详情信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={productionConfig} labelCol={false} />
                </Form>
                {isBenLaiShengHuo ? (
                    // 本来生活渠道：产品基本信息显示为表格
                    <>
                        <PageTitle title='产品基本信息' type='primaryIcon' bmagin={16} />
                        <BaseTable
                            rowKey='key'
                            className='food-table-operation'
                            btnDisplay={(checkData: any, resetSelect: any) => {
                                return <TableHead />;
                            }}
                            columns={productTableColumns}
                            dataSource={productTableData.slice(
                                (productPageInfo.pageIndex - 1) * 5,
                                productPageInfo.pageIndex * 5
                            )}
                            loading={false}
                        />
                        <BasePagination
                            shouldShowTotal
                            showQuickJumper
                            current={productPageInfo.pageIndex}
                            pageSize={5}
                            total={productTableData.length}
                            onChange={(pageIndex) => handleProductPaginationChange(pageIndex, 5)}
                        />

                        <PageTitle title='物流信息' type='primaryIcon' bmagin={16} />
                        <BaseTable
                            rowKey='key'
                            className='food-table-operation'
                            btnDisplay={(checkData: any, resetSelect: any) => {
                                return <TableHead />;
                            }}
                            columns={logisticsTableColumns}
                            dataSource={logisticsTableData.slice(
                                (logisticsPageInfo.pageIndex - 1) * 5,
                                logisticsPageInfo.pageIndex * 5
                            )}
                            loading={false}
                        />
                        <BasePagination
                            shouldShowTotal
                            showQuickJumper
                            current={logisticsPageInfo.pageIndex}
                            pageSize={5}
                            total={logisticsTableData.length}
                            onChange={(pageIndex) => handleLogisticsPaginationChange(pageIndex, 5)}
                        />
                    </>
                ) : (
                    // 其他渠道：保持原有的表单显示方式
                    <>
                        <Form form={basicForm}>
                            <PageTitle title='产品基本信息' type='primaryIcon' bmagin={16} />
                            <FilterForm showMode itemConfig={basicInformation} labelCol={false} />
                        </Form>
                        <Form form={marketForm}>
                            <PageTitle title='物流信息' type='primaryIcon' bmagin={16} />
                            <FilterForm showMode itemConfig={marketformation} labelCol={false} />
                        </Form>
                        <Form form={returnForm}>
                            <PageTitle title='产品退货信息' type='primaryIcon' bmagin={16} />
                            <FilterForm showMode itemConfig={returnInformation} labelCol={false} />
                        </Form>
                    </>
                )}

                <Form form={chainForm}>
                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={chainConfig} labelCol={false} />
                </Form>

                {/* <Form
          name='basic'
          form={detailForm}
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          autoComplete='off'
        >
          <PageTitle title='食品信息' type='primaryIcon' />
                    <BaseFormItem configs={foodInfoConfig} />
          <PageTitle title='渠道信息' type='primaryIcon' bmagin={16} />
          <FilterForm showMode itemConfig={foodInfoConfig} labelCol={false} />

          <PageTitle title='订单详情信息' type='primaryIcon' bmagin={16} />
          <FilterForm showMode itemConfig={productionConfig} labelCol={false} />
          <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
          <FilterForm showMode itemConfig={chainConfig} labelCol={false} />
        </Form> */}
            </BaseCard>
            <ChainDetailModal
                transactionId={chainData?.transactionId}
                open={ChainDetailModalVisible}
                onCancel={() => setChainDetailModalVisible(false)}
            />
        </div>
    );
};

export default FoodDetail;
