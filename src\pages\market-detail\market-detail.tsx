/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 17:15:57
 * @LastEditTime: 2022-11-01 18:12:36
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BaseFormItem from '@components/base-form-item';
import PageTitle from '@components/page-title';
import React, { useState } from 'react';
import { Form, message } from 'antd';
import { useMutation, useQuery } from 'react-query';
import { foodDetail } from '@services/food';
import { enumeration } from './config';
import SwitchList from '@components/switch-list';
import styles from './index.module.less';
import { useLocation } from 'react-router-dom';
import { ReformChainError } from '@utils/errorCodeReform';
import dayjs from 'dayjs';
import { Image } from 'antd';
import FilterForm from '@components/filter-form';

import { FormItemImages, FormItemVideo } from '@components';
import ChainDetailModal from '@components/chain_detail_modal';
import { decryptedUrl, isArrayArr } from '@utils';
import { getSalesDetail } from '@services/land-test';

const FoodDetail = (props: any) => {
    const { state } = useLocation();
    const [detailForm] = Form.useForm();
    const [detailsForm] = Form.useForm();
    const [channelInformation] = Form.useForm();
    const [basicForm] = Form.useForm();
    const [returnForm] = Form.useForm();
    const [chainForm] = Form.useForm();

    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);
    // console.log("state",state)

    const detailquery = useQuery(
        ['detailquery9999'],
        () => {
            return getSalesDetail({
                productId: state?.id
            });
        },
        {
            async onSuccess(res) {
                console.log('res0990099', res);
                const arrayData = await Promise.all(
                    isArrayArr(res?.data?.productImg)?.map((item: any) => {
                        return decryptedUrl(item);
                    })
                );
                // setPromotionPicData(arrayData)
                // const videos = await decryptedUrl(res?.data?.productVideo);
                // detailForm.setFieldsValue({
                //   ...res.data,
                //   productImg: arrayData && arrayData.length > 0 ? arrayData : null,
                //   productVideo: videos ? videos : null,
                //   transactionTime: res?.data?.transactionTime
                //     ? dayjs(res?.data?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
                //     : '-'
                // });
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    console.log('detailquery', detailquery?.data?.data);
    const fooddetail = detailquery?.data?.data?.food;

    const basicProductData = detailquery?.data?.data?.basicProductData;
    const DetailData = detailquery?.data?.data?.orderDetailData;
    const ReturnData = detailquery?.data?.data?.ProductReturnData;
    const chainData = detailquery?.data?.data?.chainData;

    // 渠道信息
    const foodInfoConfig = [
        {
            label: '渠道名称',
            name: 'channelName',
            value: 'channelName',
            type: 'ShowText'
        }
    ];
    // 订单详情信息
    const productionConfig = [
        {
            label: '订单编号',
            name: 'orderId',
            value: 'orderId',
            type: 'ShowText',
            span: 8
        },
        {
            label: '购买数量',
            name: 'qtySku',
            value: 'qtySku',
            type: 'ShowText',
            span: 8
        },
        {
            label: '包裹编号',
            name: 'packageId',
            value: 'packageId',
            type: 'ShowText',
            span: 8
        },
        {
            label: '供应商SKU编号',
            name: 'supplierSkuCode',
            value: 'supplierSkuCode',
            type: 'ShowText',
            span: 8
        },
        {
            label: '供应商名称',
            name: 'supplierName',
            value: 'supplierName',
            type: 'ShowText',
            span: 8
        },
        {
            label: '供应商编码',
            name: 'supplierId',
            value: 'supplierId',
            type: 'ShowText',
            span: 8
        },
        {
            label: '销售总价',
            name: 'finalPrice',
            value: 'finalPrice',
            type: 'ShowText',
            span: 8
        },
        {
            label: '下单时间',
            name: 'orderTime',
            value: 'orderTime',
            type: 'ShowText',
            span: 8
        },

        {
            label: '支付时间',
            name: 'payTime',
            value: 'payTime',
            type: 'ShowText',
            span: 8
        },
        {
            label: '完成时间',
            name: 'finishTime',
            value: 'finishTime',
            type: 'ShowText',
            span: 8
        }
    ];
    // 产品基本信息
    const basicInformation = [
        {
            label: '商品名称',
            name: 'spuName',
            value: 'spuName',
            type: 'ShowText',
            span: 8
        },
        {
            label: '产品品类',
            name: 'productCategory',
            value: 'productCategory',
            type: 'ShowText',
            span: 8
        },
        {
            label: '规格',
            name: 'skuSpecs',
            value: 'skuSpecs',
            type: 'ShowText',
            span: 8
        },
        {
            label: '品牌',
            name: 'brandId',
            value: 'brandId',
            type: 'ShowText',
            span: 8
        },
        {
            label: '销售单价',
            name: 'primiPrice',
            value: 'primiPrice',
            type: 'ShowText',
            span: 8
        },
        {
            label: '平台sku编码',
            name: 'skuId',
            value: 'skuId',
            type: 'ShowText',
            span: 8
        }
    ];
    // 产品退货信息
    const returnInformation = [
        {
            label: '售后数量',
            name: 'skuNum',
            value: 'skuNum',
            type: 'ShowText',
            span: 8
        },
        {
            label: '退款金额',
            name: 'refundAmount',
            value: 'refundAmount',
            type: 'ShowText',
            span: 8
        },
        {
            label: '申请时间',
            name: 'applyTime',
            value: 'applyTime',
            type: 'ShowText',
            span: 8
        },
        {
            label: '退款完成时间',
            name: 'channelPayTime',
            value: 'channelPayTime',
            type: 'ShowText',
            span: 8
        },
        {
            label: '售后类型',
            name: 'postsaleType',
            value: 'postsaleType',
            type: 'ShowText',
            span: 8
        }
    ];
    const chainConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: 'transactionId',
            title: '信息的链上的哈希值',
            type: 'Link',
            onClick: () => {
                setChainDetailModalVisible(true);
            }
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: 'transactionTime',
            title: '信息上链的时间',
            type: 'ShowText'
        }
    ];
    chainForm.setFieldsValue({
        transactionId: chainData?.transactionId,
        transactionTime: chainData?.transactionTime
            ? dayjs(chainData?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
            : '-'
    });
    //渠道信息
    channelInformation.setFieldsValue({
        channelName: detailquery?.data?.data?.channelName
    });
    // 订单详情信息
    detailsForm.setFieldsValue({
        orderId: DetailData?.orderId,
        qtySku: DetailData?.qtySku,
        packageId: DetailData?.packageId,
        supplierSkuCode: DetailData?.supplierSkuCode,
        supplierName: DetailData?.supplierName,
        supplierId: DetailData?.supplierId,
        finalPrice: DetailData?.finalPrice,
        orderTime: DetailData?.orderTime ? dayjs(DetailData?.orderTime).format('YYYY-MM-DD HH:mm:ss') : '-',
        finishTime: DetailData?.finishTime ? dayjs(DetailData?.finishTime).format('YYYY-MM-DD HH:mm:ss') : '-'
    });
    // 产品基本信息
    basicForm.setFieldsValue({
        spuName: basicProductData?.spuName,
        productCategory: basicProductData?.productCategory,
        skuSpecs: basicProductData?.skuSpecs ? JSON.parse(basicProductData?.skuSpecs).spec['重量'] : '-',
        brandId: basicProductData?.brandId,
        primiPrice: basicProductData?.primiPrice,
        skuId: basicProductData?.skuId
    });
    // 产品退货信息
    returnForm.setFieldsValue({
        skuNum: ReturnData?.skuNum ? ReturnData?.skuNum : '暂无',
        refundAmount: ReturnData?.refundAmount ? ReturnData?.refundAmount : '暂无',
        applyTime: ReturnData?.applyTime ? dayjs(ReturnData?.applyTime).format('YYYY-MM-DD HH:mm:ss') : '暂无',
        channelPayTime: ReturnData?.channelPayTime
            ? dayjs(ReturnData?.channelPayTime).format('YYYY-MM-DD HH:mm:ss')
            : '暂无',
        postsaleType: ReturnData?.postsaleType ? ReturnData?.postsaleType : '暂无'
    });

    const onFinish = (values: any) => {
        console.log('Success:', values);
    };

    const onFinishFailed = (errorInfo: any) => {
        console.log('Failed:', errorInfo);
    };
    console.log('detailquery?.data?.data?.material', detailquery?.data?.data?.material);
    return (
        <div>
            <BaseCard title={<PageTitle title='销售信息详情' bg='container chan' />}>
                <Form form={channelInformation}>
                    <PageTitle title='渠道信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={foodInfoConfig} labelCol={false} />
                </Form>
                <Form form={detailsForm}>
                    <PageTitle title='订单详情信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={productionConfig} labelCol={false} />
                </Form>
                <Form form={basicForm}>
                    <PageTitle title='产品基本信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={basicInformation} labelCol={false} />
                </Form>
                <Form form={returnForm}>
                    <PageTitle title='产品退货信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={returnInformation} labelCol={false} />
                </Form>
                <Form form={chainForm}>
                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={chainConfig} labelCol={false} />
                </Form>

                {/* <Form
          name='basic'
          form={detailForm}
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          autoComplete='off'
        >
          <PageTitle title='食品信息' type='primaryIcon' />
                    <BaseFormItem configs={foodInfoConfig} />
          <PageTitle title='渠道信息' type='primaryIcon' bmagin={16} />
          <FilterForm showMode itemConfig={foodInfoConfig} labelCol={false} />

          <PageTitle title='订单详情信息' type='primaryIcon' bmagin={16} />
          <FilterForm showMode itemConfig={productionConfig} labelCol={false} />
          <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
          <FilterForm showMode itemConfig={chainConfig} labelCol={false} />
        </Form> */}
            </BaseCard>
            <ChainDetailModal
                transactionId={chainData?.transactionId}
                open={ChainDetailModalVisible}
                onCancel={() => setChainDetailModalVisible(false)}
            />
        </div>
    );
};

export default FoodDetail;
